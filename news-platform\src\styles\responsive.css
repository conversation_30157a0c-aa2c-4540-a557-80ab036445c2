/* Mobile-first responsive optimizations */

/* Smooth scrolling for all devices */
html {
  scroll-behavior: smooth;
}

/* Mobile text visibility fixes for story pages */
@media (max-width: 768px) {
  /* Force text visibility on mobile story pages */
  .prose p {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
  }

  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
  }

  .prose strong, .prose b {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
  }

  .prose li {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
  }

  /* Story page specific text fixes */
  .text-gray-900, .text-gray-800, .text-gray-700 {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
  }

  .text-gray-600, .text-gray-500 {
    color: #374151 !important;
    -webkit-text-fill-color: #374151 !important;
  }

  /* Ensure article content is visible */
  article * {
    color: inherit !important;
  }

  /* Force background colors for better contrast */
  .bg-white {
    background-color: #ffffff !important;
  }

  /* Ensure all text elements are visible */
  * {
    -webkit-text-fill-color: inherit !important;
  }

  /* Specific mobile text rendering fixes */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Force text visibility for all elements */
  h1, h2, h3, h4, h5, h6, p, span, div, a, li, td, th {
    color: inherit !important;
    -webkit-text-fill-color: inherit !important;
  }
}

/* Optimize touch targets for mobile */
@media (max-width: 768px) {
  button, a {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Improve text readability on mobile */
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
  
  /* Optimize carousel for mobile */
  .carousel-container {
    height: 300px !important;
  }
  
  /* Better spacing for mobile cards */
  .story-card {
    margin-bottom: 1rem;
  }
  
  /* Optimize header for mobile */
  .mobile-header {
    padding: 0.5rem 1rem;
  }
  
  /* Better mobile navigation */
  .category-nav {
    padding: 0.5rem;
    gap: 0.5rem;
  }
  
  .category-nav button {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .carousel-container {
    height: 400px !important;
  }
  
  /* Better grid layouts for tablet */
  .stories-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
  .carousel-container {
    height: 500px !important;
  }
  
  /* Optimize sidebar for desktop */
  .sidebar {
    position: sticky;
    top: 6rem;
    height: fit-content;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .story-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}

/* Focus styles for accessibility */
button:focus,
a:focus,
input:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Utility classes for responsive design */
.container-responsive {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 2rem;
  }
}

/* Responsive typography */
.text-responsive-xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

@media (min-width: 640px) {
  .text-responsive-xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

@media (min-width: 1024px) {
  .text-responsive-xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

/* Responsive spacing */
.spacing-responsive {
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .spacing-responsive {
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .spacing-responsive {
    margin-bottom: 2rem;
  }
}

/* Optimize images for different screen sizes */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.responsive-image:hover {
  transform: scale(1.05);
}

/* Better scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Hide scrollbar for mobile horizontal scroll */
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
